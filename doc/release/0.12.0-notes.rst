==========================
SciPy 0.12.0 Release Notes
==========================

.. contents::

SciPy 0.12.0 is the culmination of 7 months of hard work. It contains
many new features, numerous bug-fixes, improved test coverage and
better documentation.  There have been a number of deprecations and
API changes in this release, which are documented below.  All users
are encouraged to upgrade to this release, as there are a large number
of bug-fixes and optimizations.  Moreover, our development attention
will now shift to bug-fix releases on the 0.12.x branch, and on adding
new features on the master branch.

Some of the highlights of this release are:

  - Completed QHull wrappers in scipy.spatial.
  - cKDTree now a drop-in replacement for KDTree.
  - A new global optimizer, basinhopping.
  - Support for Python 2 and Python 3 from the same code base (no more 2to3).

This release requires Python 2.6, 2.7 or 3.1-3.3 and NumPy 1.5.1 or greater.
Support for Python 2.4 and 2.5 has been dropped as of this release.


New features
============

``scipy.spatial`` improvements
------------------------------

cKDTree feature-complete
^^^^^^^^^^^^^^^^^^^^^^^^
Cython version of KDTree, cKDTree, is now feature-complete.  Most operations
(construction, query, query_ball_point, query_pairs, count_neighbors and
sparse_distance_matrix) are between 200 and 1000 times faster in cKDTree than
in KDTree.  With very minor caveats, cKDTree has exactly the same interface as
KDTree, and can be used as a drop-in replacement.

Voronoi diagrams and convex hulls
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
`scipy.spatial` now contains functionality for computing Voronoi
diagrams and convex hulls using the Qhull library. (Delaunay
triangulation was available since Scipy 0.9.0.)

Delaunay improvements
^^^^^^^^^^^^^^^^^^^^^
It's now possible to pass in custom Qhull options in Delaunay
triangulation. Coplanar points are now also recorded, if present.
Incremental construction of Delaunay triangulations is now also
possible.

Spectral estimators (``scipy.signal``)
--------------------------------------
The functions ``scipy.signal.periodogram`` and ``scipy.signal.welch`` were
added, providing DFT-based spectral estimators.


``scipy.optimize`` improvements
-------------------------------

Callback functions in L-BFGS-B and TNC
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
A callback mechanism was added to L-BFGS-B and TNC minimization solvers.

Basin hopping global optimization (``scipy.optimize.basinhopping``)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
A new global optimization algorithm.  Basinhopping is designed to efficiently
find the global minimum of a smooth function.


``scipy.special`` improvements
------------------------------

Revised complex error functions
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
The computation of special functions related to the error function now uses a
new `Faddeeva library from MIT <http://ab-initio.mit.edu/Faddeeva>`__ which
increases their numerical precision. The scaled and imaginary error functions
``erfcx`` and ``erfi`` were also added, and the Dawson integral ``dawsn`` can
now be evaluated for a complex argument.

Faster orthogonal polynomials
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Evaluation of orthogonal polynomials (the ``eval_*`` routines) in now
faster in ``scipy.special``, and their ``out=`` argument functions
properly.


``scipy.sparse.linalg`` features
--------------------------------
- In ``scipy.sparse.linalg.spsolve``, the ``b`` argument can now be either
  a vector or a matrix.
- ``scipy.sparse.linalg.inv`` was added.  This uses ``spsolve`` to compute
  a sparse matrix inverse.
- ``scipy.sparse.linalg.expm`` was added.  This computes the exponential of
  a sparse matrix using a similar algorithm to the existing dense array
  implementation in ``scipy.linalg.expm``.


Listing Matlab(R) file contents in ``scipy.io``
-----------------------------------------------
A new function ``whosmat`` is available in ``scipy.io`` for inspecting contents
of MAT files without reading them to memory.


Documented BLAS and LAPACK low-level interfaces (``scipy.linalg``)
------------------------------------------------------------------
The modules `scipy.linalg.blas` and `scipy.linalg.lapack` can be used
to access low-level BLAS and LAPACK functions.


Polynomial interpolation improvements (``scipy.interpolate``)
-------------------------------------------------------------
The barycentric, Krogh, piecewise and pchip polynomial interpolators in
``scipy.interpolate`` accept now an ``axis`` argument.


Deprecated features
===================

`scipy.lib.lapack`
------------------
The module `scipy.lib.lapack` is deprecated. You can use `scipy.linalg.lapack`
instead. The module `scipy.lib.blas` was deprecated earlier in Scipy 0.10.0.


`fblas` and `cblas`
-------------------
Accessing the modules `scipy.linalg.fblas`, `cblas`, `flapack`, `clapack` is
deprecated. Instead, use the modules `scipy.linalg.lapack` and
`scipy.linalg.blas`.


Backwards incompatible changes
==============================

Removal of ``scipy.io.save_as_module``
--------------------------------------
The function ``scipy.io.save_as_module`` was deprecated in Scipy 0.11.0, and is
now removed.

Its private support modules ``scipy.io.dumbdbm_patched`` and
``scipy.io.dumb_shelve`` are also removed.

`axis` argument added to `scipy.stats.scoreatpercentile`
--------------------------------------------------------

The function `scipy.stats.scoreatpercentile` has been given an `axis`
argument.  The default argument is `axis=None`, which means the calculation
is done on the flattened array.  Before this change, `scoreatpercentile`
would act as if `axis=0` had been given.  Code using `scoreatpercentile`
with a multidimensional array will need to add `axis=0` to the function call
to preserve the old behavior.  (This API change was not noticed until
long after the release of 0.12.0.)


Authors
=======
* Anton Akhmerov +
* Alexander Eberspächer +
* Anne Archibald
* Jisk Attema +
* K.-Michael Aye +
* bemasc +
* Sebastian Berg +
* François Boulogne +
* Matthew Brett
* Lars Buitinck
* Steven Byrnes +
* Tim Cera +
* Christian +
* Keith Clawson +
* David Cournapeau
* Nathan Crock +
* endolith
* Bradley M. Froehle +
* Matthew R Goodman
* Christoph Gohlke
* Ralf Gommers
* Robert David Grant +
* Yaroslav Halchenko
* Charles Harris
* Jonathan Helmus
* Andreas Hilboll
* Hugo +
* Oleksandr Huziy
* Jeroen Demeyer +
* Johannes Schönberger +
* Steven G. Johnson +
* Chris Jordan-Squire
* Jonathan Taylor +
* Niklas Kroeger +
* Jerome Kieffer +
* kingson +
* Josh Lawrence
* Denis Laxalde
* Alex Leach +
* Tim Leslie
* Richard Lindsley +
* Lorenzo Luengo +
* Stephen McQuay +
* MinRK
* Sturla Molden +
* Eric Moore +
* mszep +
* Matt Newville +
* Vlad Niculae
* Travis Oliphant
* David Parker +
* Fabian Pedregosa
* Josef Perktold
* Zach Ploskey +
* Alex Reinhart +
* Gilles Rochefort +
* Ciro Duran Santillli +
* Jan Schlueter +
* Jonathan Scholz +
* Anthony Scopatz
* Skipper Seabold
* Fabrice Silva +
* Scott Sinclair
* Jacob Stevenson +
* Sturla Molden +
* Julian Taylor +
* thorstenkranz +
* John Travers +
* True Price +
* Nicky van Foreest
* Jacob Vanderplas
* Patrick Varilly
* Daniel Velkov +
* Pauli Virtanen
* Stefan van der Walt
* Warren Weckesser

A total of 75 people contributed to this release.
People with a "+" by their names contributed a patch for the first time.

