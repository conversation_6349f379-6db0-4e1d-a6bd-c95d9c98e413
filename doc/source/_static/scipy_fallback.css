@import "default.css";

/**
 * Spacing fixes
 */

div.body p, div.body dd, div.body li {
  line-height: 125%;
}

ul.simple { 
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
}

/* spacing around blockquoted fields in parameters/attributes/returns */
td.field-body > blockquote {
    margin-top: 0.1em;
    margin-bottom: 0.5em;
}

/* spacing around example code */
div.highlight > pre {
    padding: 2px 5px 2px 5px;
}

/* spacing in see also definition lists */
dl.last > dd {
    margin-top: 1px;
    margin-bottom: 5px;
    margin-left: 30px;
}

/* hide overflowing content in the sidebar */
div.sphinxsidebarwrapper p.topless {
    overflow: hidden;
}

/**
 * Hide dummy toctrees 
 */

ul { 
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}
ul li { 
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}
ul li a.reference { 
  padding-top: 0;
  padding-bottom: 0;
  margin-top: 0;
  margin-bottom: 0;
}

/**
 * Make high-level subsections easier to distinguish from top-level ones
 */
div.body h3 { 
  background-color: transparent;
}

div.body h4 { 
  border: none;
  background-color: transparent;
}

/** 
 * Scipy colors
 */

body { 
  background-color: rgb(100,135,220);
}

div.document { 
  background-color: rgb(230,230,230);
}

div.sphinxsidebar {
  background-color: rgb(230,230,230);
  overflow: hidden;
}

div.related {
  background-color: rgb(100,135,220);
}

div.sphinxsidebar h3 {
  color: rgb(0,102,204);
}

div.sphinxsidebar h3 a {
  color: rgb(0,102,204);
}

div.sphinxsidebar h4 {
  color: rgb(0,82,194);
}

div.sphinxsidebar p {
  color: black;
}

div.sphinxsidebar a {
  color: #355f7c;
}

div.sphinxsidebar ul.want-points {
  list-style: disc;
}

.field-list th {
  color: rgb(0,102,204);
  white-space: nowrap;
}

/** 
 * Extra admonitions 
 */

div.tip {
  background-color: #ffffe4;
  border: 1px solid #ee6;
}

div.plot-output { 
  clear-after: both;
}

div.plot-output .figure { 
  float: left;
  text-align: center;
  margin-bottom: 0;
  padding-bottom: 0;
}

div.plot-output .caption { 
  margin-top: 2;
  padding-top: 0;
}

div.plot-output:after { 
  content: ""; 
  display: block; 
  height: 0; 
  clear: both; 
}


/*
div.admonition-example {
    background-color: #e4ffe4;
    border: 1px solid #ccc;
}*/


/**
 * Styling for field lists
 */

table.field-list th {
  border-left: 1px solid #aaa !important;
  padding-left: 5px;
}

table.field-list { 
  border-collapse: separate;
  border-spacing: 10px;
}

/** 
 * Styling for footnotes
 */

table.footnote td, table.footnote th {
  border: none;
}
