{#
    basic/searchbox.html
    ~~~~~~~~~~~~~~~~~~~~

    Sphinx sidebar template: quick search box.

    :copyright: Copyright 2007-2018 by the Sphinx team, see AUTHORS.
    :license: BSD, see LICENSE for details.
#}
{%- if pagename != "search" and builder != "singlehtml" %}
<div id="searchbox" style="display: none" role="search">
  <h4>{{ _('Quick search') }}</h4>
    <div>
    <form class="search" action="{{ pathto('search') }}" method="get">
      <input type="text" style="width: inherit;" name="q" />
      <input type="submit" value="{{ _('search') }}" />
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
    </div>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
{%- endif %}