SciPy Code of Conduct
=====================


Introduction
------------

This code of conduct applies to all spaces managed by the SciPy project,
including all public and private mailing lists, issue trackers, wikis, blogs,
Twitter, and any other communication channel used by our community.  The SciPy
project does not organise in-person events, however events related to our
community should have a code of conduct similar in spirit to this one.

This code of conduct should be honored by everyone who participates in
the SciPy community formally or informally, or claims any affiliation with the
project, in any project-related activities and especially when representing the
project, in any role.

This code is not exhaustive or complete. It serves to distill our common
understanding of a collaborative, shared environment and goals. Please try to
follow this code in spirit as much as in letter, to create a friendly and
productive environment that enriches the surrounding community.


Specific Guidelines
-------------------

We strive to:

1. Be open. We invite anyone to participate in our community. We prefer to use
   public methods of communication for project-related messages, unless
   discussing something sensitive. This applies to messages for help or
   project-related support, too; not only is a public support request much more
   likely to result in an answer to a question, it also ensures that any
   inadvertent mistakes in answering are more easily detected and corrected.

2. Be empathetic, welcoming, friendly, and patient. We work together to resolve
   conflict, and assume good intentions. We may all experience some frustration
   from time to time, but we do not allow frustration to turn into a personal
   attack. A community where people feel uncomfortable or threatened is not a
   productive one.

3. Be collaborative. Our work will be used by other people, and in turn we will
   depend on the work of others. When we make something for the benefit of the
   project, we are willing to explain to others how it works, so that they can
   build on the work to make it even better. Any decision we make will affect
   users and colleagues, and we take those consequences seriously when making
   decisions.

4. Be inquisitive. Nobody knows everything! Asking questions early avoids many
   problems later, so we encourage questions, although we may direct them to
   the appropriate forum. We will try hard to be responsive and helpful.

5. Be careful in the words that we choose.  We are careful and respectful in
   our communication and we take responsibility for our own speech. Be kind to
   others. Do not insult or put down other participants.  We will not accept
   harassment or other exclusionary behaviour, such as:

  - Violent threats or language directed against another person.
  - Sexist, racist, or otherwise discriminatory jokes and language.
  - Posting sexually explicit or violent material.
  - Posting (or threatening to post) other people's personally identifying information ("doxing").
  - Sharing private content, such as emails sent privately or non-publicly,
    or unlogged forums such as IRC channel history, without the sender's consent.
  - Personal insults, especially those using racist or sexist terms.
  - Unwelcome sexual attention.
  - Excessive profanity. Please avoid swearwords; people differ greatly in their sensitivity to swearing.
  - Repeated harassment of others. In general, if someone asks you to stop, then stop.
  - Advocating for, or encouraging, any of the above behaviour.


Diversity Statement
-------------------

The SciPy project welcomes and encourages participation by everyone. We are
committed to being a community that everyone enjoys being part of. Although
we may not always be able to accommodate each individual's preferences, we try
our best to treat everyone kindly.

No matter how you identify yourself or how others perceive you: we welcome you.
Though no list can hope to be comprehensive, we explicitly honour diversity in:
age, culture, ethnicity, genotype, gender identity or expression, language,
national origin, neurotype, phenotype, political beliefs, profession, race,
religion, sexual orientation, socioeconomic status, subculture and technical
ability, to the extent that these do not conflict with this code of conduct.


Though we welcome people fluent in all languages, SciPy development is
conducted in English.

Standards for behaviour in the SciPy community are detailed in the Code of
Conduct above. Participants in our community should uphold these standards
in all their interactions and help others to do so as well (see next section).


Reporting Guidelines
--------------------

We know that it is painfully common for internet communication to start at or
devolve into obvious and flagrant abuse.  We also recognize that sometimes
people may have a bad day, or be unaware of some of the guidelines in this Code
of Conduct. Please keep this in mind when deciding on how to respond to a
breach of this Code.

For clearly intentional breaches, report those to the Code of Conduct committee
(see below). For possibly unintentional breaches, you may reply to the person
and point out this code of conduct (either in public or in private, whatever is
most appropriate). If you would prefer not to do that, please feel free to
report to the Code of Conduct Committee directly, or ask the Committee for
advice, in confidence.

You can report issues to the SciPy Code of Conduct committee, at
<EMAIL>. Currently, the committee consists of:

- Stefan van der Walt
- Nathaniel J. Smith
- Ralf Gommers

If your report involves any members of the committee, or if they feel they have
a conflict of interest in handling it, then they will recuse themselves from
considering your report. Alternatively, if for any reason you feel
uncomfortable making a report to the committee, then you can also contact:

- Chair of the SciPy Steering Committee: Ralf Gommers, or
- Senior `NumFOCUS staff <https://numfocus.org/code-of-conduct#persons-responsible>`__: <EMAIL>


Incident reporting resolution & Code of Conduct enforcement
-----------------------------------------------------------

*This section summarizes the most important points, more details can be found
in* :ref:`CoC_reporting_manual`.

We will investigate and respond to all complaints. The SciPy Code of Conduct
Committee and the SciPy Steering Committee (if involved) will protect the
identity of the reporter, and treat the content of complaints as confidential
(unless the reporter agrees otherwise).

In case of severe and obvious breaches, e.g. personal threat or violent, sexist
or racist language, we will immediately disconnect the originator from SciPy
communication channels; please see the manual for details.

In cases not involving clear severe and obvious breaches of this code of
conduct, the process for acting on any received code of conduct violation
report will be:

1. acknowledge report is received
2. reasonable discussion/feedback
3. mediation (if feedback didn't help, and only if both reporter and reportee agree to this)
4. enforcement via transparent decision (see :ref:`CoC_resolutions`) by the
   Code of Conduct Committee

The committee will respond to any report as soon as possible, and at most
within 72 hours.


Endnotes
--------

We are thankful to the groups behind the following documents, from which we
drew content and inspiration:

- `The Apache Foundation Code of Conduct <https://www.apache.org/foundation/policies/conduct.html>`_
- `The Contributor Covenant <https://www.contributor-covenant.org/version/1/4/code-of-conduct/>`_
- `Jupyter Code of Conduct <https://github.com/jupyter/governance/tree/master/conduct>`_
- `Open Source Guides - Code of Conduct <https://opensource.guide/code-of-conduct/>`_

