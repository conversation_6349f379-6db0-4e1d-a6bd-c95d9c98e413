:orphan:

.. _using-git:
.. _git-development:

=====================
 Git for development
=====================

These pages describe a general git_ and github_ workflow.

This is not a comprehensive git_ reference. It's tailored to working with SciPy
and using the github_ hosting service. You may well find better or quicker ways
of getting stuff done with git_, but these should get you started.

For general resources for learning git_ see :ref:`git-resources`.

Have a look at the github_ install help pages available from `github help`_

.. _install-git:


Contents:

.. toctree::
   :maxdepth: 2

   git_intro
   following_latest
   development_setup
   configure_git
   useful_git
   git_resources
   dot2_dot3

.. include:: git_links.inc
