.. This (-*- rst -*-) format file contains commonly used link targets
   and name substitutions.  It may be included in many files,
   therefore it should only contain link targets and name
   substitutions.  Try grepping for "^\.\. _" to find plausible
   candidates for this list.

.. NOTE: reST targets are
   __not_case_sensitive__, so only one target definition is needed for
   nipy, NIPY, Nipy, etc...

.. git stuff
.. _git: https://git-scm.com/
.. _github: https://github.com/scipy/scipy
.. _github help: https://help.github.com
.. _`install it`: https://git-scm.com/downloads
.. _subversion: http://subversion.tigris.org/
.. _git cheat sheet: http://cheat.errtheblog.com/s/git
.. _pro git book: https://git-scm.com/book/
.. _git svn crash course: https://git-scm.com/course/svn.html
.. _learn.github: https://learn.github.com/
.. _network graph visualizer: https://github.com/blog/39-say-hello-to-the-network-graph-visualizer
.. _git user manual: https://www.kernel.org/pub/software/scm/git/docs/user-manual.html
.. _git tutorial: https://www.kernel.org/pub/software/scm/git/docs/gittutorial.html
.. _git community book: https://book.git-scm.com/
.. _git ready: http://www.gitready.com/
.. _git casts: http://www.gitcasts.com/
.. _Fernando's git page: http://www.fperez.org/py4science/git.html
.. _git magic: http://www-cs-students.stanford.edu/~blynn/gitmagic/index.html
.. _git concepts: http://www.eecs.harvard.edu/~cduan/technical/git/
.. _git clone: https://www.kernel.org/pub/software/scm/git/docs/git-clone.html
.. _git checkout: https://www.kernel.org/pub/software/scm/git/docs/git-checkout.html
.. _git commit: https://www.kernel.org/pub/software/scm/git/docs/git-commit.html
.. _git push: https://www.kernel.org/pub/software/scm/git/docs/git-push.html
.. _git pull: https://www.kernel.org/pub/software/scm/git/docs/git-pull.html
.. _git add: https://www.kernel.org/pub/software/scm/git/docs/git-add.html
.. _git status: https://www.kernel.org/pub/software/scm/git/docs/git-status.html
.. _git diff: https://www.kernel.org/pub/software/scm/git/docs/git-diff.html
.. _git log: https://www.kernel.org/pub/software/scm/git/docs/git-log.html
.. _git branch: https://www.kernel.org/pub/software/scm/git/docs/git-branch.html
.. _git remote: https://www.kernel.org/pub/software/scm/git/docs/git-remote.html
.. _git config: https://www.kernel.org/pub/software/scm/git/docs/git-config.html
.. _why the -a flag?: http://www.gitready.com/beginner/2009/01/18/the-staging-area.html
.. _git staging area: http://www.gitready.com/beginner/2009/01/18/the-staging-area.html
.. _tangled working copy problem: https://tomayko.com/writings/the-thing-about-git
.. _git management: http://kerneltrap.org/Linux/Git_Management
.. _linux git workflow: https://www.mail-archive.com/<EMAIL>/msg39091.html
.. _ipython git workflow: http://mail.python.org/pipermail/ipython-dev/2010-October/006746.html
.. _git parable: http://tom.preston-werner.com/2009/05/19/the-git-parable.html
.. _git foundation: http://matthew-brett.github.com/pydagogue/foundation.html
.. _numpy/master: https://github.com/numpy/numpy
.. _git cherry-pick: https://www.kernel.org/pub/software/scm/git/docs/git-cherry-pick.html
.. _git blame: https://www.kernel.org/pub/software/scm/git/docs/git-blame.html
.. _this blog post: https://github.com/blog/612-introducing-github-compare-view
.. _this article on merging conflicts:  https://git-scm.com/book/en/Git-Branching-Basic-Branching-and-Merging#Basic-Merge-Conflicts
.. _learn git: https://www.atlassian.com/git/tutorials/
.. _filing pull requests: https://help.github.com/articles/using-pull-requests/#initiating-the-pull-request
.. _pull request review: https://help.github.com/articles/using-pull-requests/#reviewing-the-pull-request


.. other stuff
.. _python: https://www.python.org
.. _NumPy: https://www.numpy.org
.. _`NumPy github`: https://github.com/numpy/numpy
.. _`NumPy mailing list`: https://scipy.org/scipylib/mailing-lists.html
.. _SciPy: https://www.scipy.org/scipylib/index.html
.. _`SciPy github`: https://github.com/scipy/scipy
.. _`SciPy mailing list`: https://scipy.org/scipylib/mailing-lists.html
.. _`SciPy repository`: https://github.com/scipy/scipy