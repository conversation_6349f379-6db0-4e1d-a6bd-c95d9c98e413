.. _git-resources:

=========================
Additional Git_ Resources
=========================

Tutorials and summaries
=======================

* `github help`_ has an excellent series of how-to guides.
* `learn.github`_ has an excellent series of tutorials
* The `pro git book`_ is a good in-depth book on git.
* A `git cheat sheet`_ is a page giving summaries of common commands.
* The `git user manual`_
* The `git tutorial`_
* The `git community book`_
* `git ready`_ - a nice series of tutorials
* `git casts`_ - video snippets giving git how-tos.
* `git magic`_ - extended introduction with intermediate detail
* The `git parable`_ is an easy read explaining the concepts behind git.
* Our own `git foundation`_ expands on the `git parable`_.
* <PERSON>' git page - `<PERSON>'s git page`_ - many links and tips
* A good but technical page on `git concepts`_
* `git svn crash course`_: git_ for those of us used to subversion_

Advanced git workflow
=====================

There are many ways of working with git_; here are some posts on the
rules of thumb that other projects have come up with:

* Linus Torvalds on `git management`_
* Linus Torvalds on `linux git workflow`_ .  Summary; use the git tools
  to make the history of your edits as clean as possible; merge from
  upstream edits as little as possible in branches where you are doing
  active development.

Manual pages online
===================

You can get these on your own machine with (e.g) ``git help push`` or
(same thing) ``git push --help``, but, for convenience, here are the
online manual pages for some common commands:

* `git add`_
* `git branch`_
* `git checkout`_
* `git clone`_
* `git commit`_
* `git config`_
* `git diff`_
* `git log`_
* `git pull`_
* `git push`_
* `git remote`_
* `git status`_

.. include:: git_links.inc
