
.. _continuous-uniform:

Uniform Distribution
====================

Standard form :math:`x\in\left[0,1\right].` In general form, the lower limit is :math:`L,` the upper limit is :math:`S+L.`

.. math::
   :nowrap:

    \begin{eqnarray*} f\left(x\right) & = & 1\\ F\left(x\right) & = & x\\ G\left(q\right) & = & q\end{eqnarray*}

.. math::
   :nowrap:

    \begin{eqnarray*} \mu & = & \frac{1}{2}\\ \mu_{2} & = & \frac{1}{12}\\ \gamma_{1} & = & 0\\ \gamma_{2} & = & -\frac{6}{5}\end{eqnarray*}

.. math::

     h\left[X\right]=0

Implementation: `scipy.stats.uniform`
